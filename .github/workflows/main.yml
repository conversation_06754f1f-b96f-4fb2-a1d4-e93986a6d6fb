name: Rust CI/CD

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always
  IMAGE_NAME: ghcr.io/${{ github.repository }}

permissions:
  contents: write
  packages: write

concurrency:
  cancel-in-progress: true
  group: ${{ github.workflow }}-${{ github.ref }}

jobs:
  fmt:
    name: Format
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
          rustup component add rustfmt
      - name: Rustfmt Check
        uses: actions-rust-lang/rustfmt@v1

  deps:
    name: Dependencies
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
      - name: Machete
        uses: bnjbvr/cargo-machete@7959c845782fed02ee69303126d4a12d64f1db18

  typos:
    name: Typo<PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
      - uses: taiki-e/cache-cargo-install-action@v2
        with:
          tool: typos-cli
      - name: Typos Check
        run: typos src/

  lint:
    name: Lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Install Build Tools
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf libpcap-dev
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: "build-cache-${{ runner.os }}-${{ runner.arch }}-stable"
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
          rustup component add clippy
      - name: Cargo Clippy (Deny Warnings)
        run: cargo clippy --all-targets --locked -- -D warnings
      - name: Cargo Clippy (Allow Warnings)
        run: cargo clippy --all-targets --locked

  build_deploy:
    name: Build & Deploy
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: 'ubuntu-latest'
          - platform: 'windows-latest'
    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v5
      - name: install dependencies (ubuntu only)
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf libpcap-dev
      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: "build-cache-${{ runner.os }}-${{ runner.arch }}-stable"
      - name: Update Rust toolchain to stable
        run: |
          rustup override set stable
          rustup update stable
          rustup component add clippy
      - name: Configure GPG Key
        if: matrix.platform == 'ubuntu-latest'
        run: |
          echo -n "$GPG_SIGNING_KEY" | base64 --decode | gpg --import
        env:
          GPG_SIGNING_KEY: ${{ secrets.GPG_SIGNING_KEY }}
      - uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SIGN: 1
          APPIMAGETOOL_FORCE_SIGN: 1
          SIGN_KEY: ${{ secrets.SIGN_KEY }}
        with:
          tagName: app-v__VERSION__ # the action automatically replaces \_\_VERSION\_\_ with the app version.
          releaseName: 'App v__VERSION__'
          releaseBody: 'See the assets to download this version and install.'
          releaseDraft: true
          prerelease: false
