---
name: Feature request
about: Suggest an idea for this project
title: '[FEATURE] '
labels: enhancement
assignees: raimannma

---

**Is your feature request related to a problem? Please describe.**
<!-- A clear and concise description of what the problem is. Ex. I'm always frustrated when [...] -->

**Describe the solution you'd like**
<!-- A clear and concise description of what you want to happen. -->

**Describe alternatives you've considered**
<!-- A clear and concise description of any alternative solutions or features you've considered. -->

**API Endpoint Design (if applicable)**
<!-- If you're proposing a new API endpoint, describe how it might work. -->

```
GET /v1/your-endpoint

Query Parameters:
- param1: description
- param2: description

Response:
{
  "field1": "value1",
  "field2": "value2"
}
```

**Additional context**
<!-- Add any other context or screenshots about the feature request here. -->
